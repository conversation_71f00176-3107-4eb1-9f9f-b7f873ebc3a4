using System;
using System.Collections.Generic;

namespace Entities.DTOs.Cache
{
    /// <summary>
    /// Tüm şirketlerin cache istatistikleri DTO
    /// </summary>
    public class AllCompaniesStatisticsDto
    {
        public int TotalCompanies { get; set; }
        public long TotalCacheKeys { get; set; }
        public long TotalMemoryUsage { get; set; }
        public double AverageKeysPerCompany { get; set; }
        public List<CompanyStatisticsDto> Companies { get; set; } = new List<CompanyStatisticsDto>();
    }

    /// <summary>
    /// Şirket cache istatistikleri DTO
    /// </summary>
    public class CompanyStatisticsDto
    {
        public int CompanyId { get; set; }
        public string CompanyName { get; set; } = string.Empty;
        public CacheStatisticsDto? Statistics { get; set; }
        public bool IsActive { get; set; }
        public DateTime CreationDate { get; set; }
    }

    /// <summary>
    /// Şirket cache detayları DTO (Multi-company)
    /// </summary>
    public class CompanyCacheDetailsDto
    {
        public object? Company { get; set; }
        public CacheDetailsDto? CacheDetails { get; set; }
    }

    /// <summary>
    /// Toplu cache işlemi request DTO
    /// </summary>
    public class BulkCacheOperationRequestDto
    {
        public List<int> CompanyIds { get; set; } = new List<int>();
        public string? Operation { get; set; }
        public Dictionary<string, object>? Parameters { get; set; }
    }

    /// <summary>
    /// Toplu cache işlemi result DTO
    /// </summary>
    public class BulkCacheOperationResultDto
    {
        public long TotalRemovedCount { get; set; }
        public int ProcessedCompanies { get; set; }
        public List<BulkOperationItemDto> Results { get; set; } = new List<BulkOperationItemDto>();
    }

    /// <summary>
    /// Toplu işlem item DTO
    /// </summary>
    public class BulkOperationItemDto
    {
        public int CompanyId { get; set; }
        public string CompanyName { get; set; } = string.Empty;
        public bool Success { get; set; }
        public long RemovedCount { get; set; }
        public string Message { get; set; } = string.Empty;
    }
}
