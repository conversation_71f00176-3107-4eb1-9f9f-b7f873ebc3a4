using System;
using System.Collections.Generic;

namespace Entities.DTOs.Cache
{
    /// <summary>
    /// Cache istatistikleri DTO
    /// Company bazlı cache kullanım bilgileri
    /// </summary>
    public class CacheStatisticsDto
    {
        public int CompanyId { get; set; }
        public int TotalKeys { get; set; }
        public long TotalMemoryUsage { get; set; }
        public double TotalMemoryUsageMB { get; set; }
        public Dictionary<string, int> KeysByEntity { get; set; } = new Dictionary<string, int>();
        public DateTime LastUpdated { get; set; }
    }
}
