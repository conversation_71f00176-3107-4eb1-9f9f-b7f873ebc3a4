using System;

namespace Entities.DTOs.Cache
{
    /// <summary>
    /// Real-time cache metrics DTO
    /// </summary>
    public class CacheMetricsDto
    {
        public int CompanyId { get; set; }
        public DateTime Timestamp { get; set; }
        public CacheStatisticsDto? Statistics { get; set; }
        public CachePerformanceDto? Performance { get; set; }
        public object? ServerInfo { get; set; }
        public object[]? TopCacheKeys { get; set; }
    }

    /// <summary>
    /// Cache performans bilgileri DTO
    /// </summary>
    public class CachePerformanceDto
    {
        public long ResponseTime { get; set; }
        public bool IsConnected { get; set; }
        public int ConnectionCount { get; set; }
    }
}
