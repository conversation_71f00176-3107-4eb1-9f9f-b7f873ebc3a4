using Entities.DTOs.Cache;
using System.Threading.Tasks;

namespace Core.CrossCuttingConcerns.Caching
{
    /// <summary>
    /// Cache metrics hesaplama servisi interface
    /// Redis-specific cache istatistikleri ve performans metrikleri
    /// </summary>
    public interface ICacheMetricsService
    {
        /// <summary>
        /// Pattern'e göre cache istatistiklerini hesaplar
        /// </summary>
        /// <param name="pattern">Cache key pattern (örn: gym:1:*)</param>
        /// <returns>Cache istatistikleri</returns>
        Task<CacheStatisticsDto> CalculateStatisticsAsync(string pattern);

        /// <summary>
        /// Cache sağlık durumunu kontrol eder
        /// </summary>
        /// <returns>Cache sağlık bilgileri</returns>
        Task<CacheHealthDto> CheckHealthAsync();

        /// <summary>
        /// Pattern'e göre cache key'lerini getirir (pagination ile)
        /// </summary>
        /// <param name="pattern">Cache key pattern</param>
        /// <param name="page">Say<PERSON> numarası</param>
        /// <param name="size">Sayfa boyutu</param>
        /// <returns>Sayfalanmış cache key'leri</returns>
        Task<PaginatedCacheKeysDto> GetKeysByPatternAsync(string pattern, int page, int size);

        /// <summary>
        /// Redis server bilgilerini getirir
        /// </summary>
        /// <returns>Server bilgileri</returns>
        Task<object> GetRedisServerInfoAsync();

        /// <summary>
        /// Cache key'inin değerini getirir
        /// </summary>
        /// <param name="key">Cache key</param>
        /// <returns>Key değer bilgileri</returns>
        Task<CacheKeyValueDto> GetKeyValueAsync(string key);

        /// <summary>
        /// En çok kullanılan cache key'lerini getirir
        /// </summary>
        /// <param name="pattern">Cache key pattern</param>
        /// <param name="count">Getirilecek key sayısı</param>
        /// <returns>Top cache key'leri</returns>
        Task<object[]> GetTopCacheKeysAsync(string pattern, int count);

        /// <summary>
        /// Real-time cache performans metrikleri hesaplar
        /// </summary>
        /// <param name="pattern">Cache key pattern</param>
        /// <returns>Performans metrikleri</returns>
        Task<CachePerformanceDto> GetPerformanceMetricsAsync(string pattern);
    }
}
