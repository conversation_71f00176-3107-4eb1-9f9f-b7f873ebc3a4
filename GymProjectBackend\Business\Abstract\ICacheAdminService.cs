using Core.Utilities.Results;
using Entities.DTOs.Cache;
using System.Threading.Tasks;

namespace Business.Abstract
{
    /// <summary>
    /// Cache admin işlemleri service interface
    /// Multi-tenant cache yönetimi için business logic
    /// </summary>
    public interface ICacheAdminService
    {
        #region Single Company Operations

        /// <summary>
        /// Company cache istatistiklerini getirir
        /// </summary>
        /// <param name="companyId">Şirket ID</param>
        /// <returns>Cache istatistikleri</returns>
        Task<IDataResult<CacheStatisticsDto>> GetCompanyCacheStatisticsAsync(int companyId);

        /// <summary>
        /// Cache sağlık durumunu kontrol eder
        /// </summary>
        /// <returns>Cache sağlık bilgileri</returns>
        Task<IDataResult<CacheHealthDto>> GetCacheHealthAsync();

        /// <summary>
        /// Company'ye ait cache key'lerini listeler
        /// </summary>
        /// <param name="companyId">Şirket ID</param>
        /// <param name="page">Sayfa numarası</param>
        /// <param name="size">Sayfa boyutu</param>
        /// <returns>Sayfalanmış cache key'leri</returns>
        Task<IDataResult<PaginatedCacheKeysDto>> GetCompanyCacheKeysAsync(int companyId, int page, int size);

        /// <summary>
        /// Belirli pattern'e göre cache key'lerini listeler
        /// </summary>
        /// <param name="companyId">Şirket ID</param>
        /// <param name="pattern">Cache key pattern</param>
        /// <param name="page">Sayfa numarası</param>
        /// <param name="size">Sayfa boyutu</param>
        /// <returns>Pattern'e göre cache key'leri</returns>
        Task<IDataResult<PaginatedCacheKeysDto>> GetKeysByPatternAsync(int companyId, string pattern, int page, int size);

        /// <summary>
        /// Company'nin tüm cache'ini temizler
        /// </summary>
        /// <param name="companyId">Şirket ID</param>
        /// <returns>Temizleme sonucu</returns>
        IResult ClearTenantCache(int companyId);

        /// <summary>
        /// Belirli pattern'deki cache'leri temizler
        /// </summary>
        /// <param name="companyId">Şirket ID</param>
        /// <param name="pattern">Cache pattern</param>
        /// <returns>Temizleme sonucu</returns>
        IResult ClearCacheByPattern(int companyId, string pattern);

        /// <summary>
        /// Company cache detaylarını getirir
        /// </summary>
        /// <param name="companyId">Şirket ID</param>
        /// <returns>Cache detayları</returns>
        Task<IDataResult<CacheDetailsDto>> GetTenantCacheDetailsAsync(int companyId);

        /// <summary>
        /// Cache warmup işlemi başlatır
        /// </summary>
        /// <param name="companyId">Şirket ID</param>
        /// <param name="request">Warmup parametreleri</param>
        /// <returns>Warmup sonucu</returns>
        Task<IDataResult<CacheWarmupResultDto>> WarmupCacheAsync(int companyId, CacheWarmupRequestDto request);

        /// <summary>
        /// Real-time cache metrics getirir
        /// </summary>
        /// <param name="companyId">Şirket ID</param>
        /// <returns>Real-time metrics</returns>
        Task<IDataResult<CacheMetricsDto>> GetRealtimeMetricsAsync(int companyId);

        /// <summary>
        /// Belirli cache key'ini siler
        /// </summary>
        /// <param name="companyId">Şirket ID</param>
        /// <param name="key">Cache key</param>
        /// <returns>Silme sonucu</returns>
        IResult DeleteCacheKey(int companyId, string key);

        /// <summary>
        /// Cache key'inin değerini getirir
        /// </summary>
        /// <param name="companyId">Şirket ID</param>
        /// <param name="key">Cache key</param>
        /// <returns>Key değeri</returns>
        Task<IDataResult<CacheKeyValueDto>> GetCacheKeyValueAsync(int companyId, string key);

        #endregion

        #region Multi-Company Operations (Owner/Admin Only)

        /// <summary>
        /// Tüm şirketlerin cache istatistiklerini getirir
        /// </summary>
        /// <returns>Tüm şirket istatistikleri</returns>
        Task<IDataResult<AllCompaniesStatisticsDto>> GetAllCompaniesStatisticsAsync();

        /// <summary>
        /// Belirli bir şirketin cache detaylarını getirir
        /// </summary>
        /// <param name="companyId">Şirket ID</param>
        /// <returns>Şirket cache detayları</returns>
        Task<IDataResult<CompanyCacheDetailsDto>> GetSpecificCompanyCacheDetailsAsync(int companyId);

        /// <summary>
        /// Belirli bir şirketin cache'ini temizler
        /// </summary>
        /// <param name="companyId">Şirket ID</param>
        /// <returns>Temizleme sonucu</returns>
        IResult ClearSpecificCompanyCache(int companyId);

        /// <summary>
        /// Çoklu şirket cache temizleme
        /// </summary>
        /// <param name="request">Toplu işlem parametreleri</param>
        /// <returns>Toplu işlem sonucu</returns>
        Task<IDataResult<BulkCacheOperationResultDto>> BulkClearCompaniesCacheAsync(BulkCacheOperationRequestDto request);

        #endregion
    }
}
