using Business.Abstract;
using Core.Utilities.Results;
using Core.Utilities.Security.CompanyContext;
using Entities.DTOs.Cache;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;

namespace WebAPI.Controllers
{
    /// <summary>
    /// Cache Admin Controller - SOLID Refactored Version
    /// Sadece HTTP request/response handling yapar
    /// Tüm business logic ICacheAdminService'e delege edildi
    /// Multi-tenant cache isolation destekler
    /// </summary>
    [Route("api/[controller]")]
    [ApiController]
    [Authorize]
    public class CacheAdminController : ControllerBase
    {
        private readonly ICacheAdminService _cacheAdminService;
        private readonly ICompanyContext _companyContext;

        public CacheAdminController(
            ICacheAdminService cacheAdminService,
            ICompanyContext companyContext)
        {
            _cacheAdminService = cacheAdminService ?? throw new ArgumentNullException(nameof(cacheAdminService));
            _companyContext = companyContext ?? throw new ArgumentNullException(nameof(companyContext));
        }

        #region Single Company Operations

        /// <summary>
        /// Cache istatistiklerini getirir (Company bazlı)
        /// </summary>
        [HttpGet("statistics")]
        public async Task<IActionResult> GetCacheStatistics()
        {
            var companyId = _companyContext.GetCompanyId();
            if (companyId <= 0)
                return BadRequest(new ErrorResult("Geçersiz company ID"));

            var result = await _cacheAdminService.GetCompanyCacheStatisticsAsync(companyId);
            return result.Success ? Ok(result) : BadRequest(result);
        }

        /// <summary>
        /// Redis bağlantı durumunu kontrol eder
        /// </summary>
        [HttpGet("health")]
        public async Task<IActionResult> GetCacheHealth()
        {
            var result = await _cacheAdminService.GetCacheHealthAsync();
            return result.Success ? Ok(result) : StatusCode(500, result);
        }

        /// <summary>
        /// Company'ye ait cache key'lerini listeler
        /// </summary>
        [HttpGet("keys")]
        public async Task<IActionResult> GetCompanyCacheKeys([FromQuery] int page = 1, [FromQuery] int size = 50)
        {
            var companyId = _companyContext.GetCompanyId();
            if (companyId <= 0)
                return BadRequest(new ErrorResult("Geçersiz company ID"));

            var result = await _cacheAdminService.GetCompanyCacheKeysAsync(companyId, page, size);
            return result.Success ? Ok(result) : BadRequest(result);
        }

        /// <summary>
        /// Belirli pattern'e göre cache key'lerini listeler
        /// </summary>
        [HttpGet("keys/pattern/{pattern}")]
        public async Task<IActionResult> GetKeysByPatternEndpoint(string pattern, [FromQuery] int page = 1, [FromQuery] int size = 50)
        {
            var companyId = _companyContext.GetCompanyId();
            if (companyId <= 0)
                return BadRequest(new ErrorResult("Geçersiz company ID"));

            var result = await _cacheAdminService.GetKeysByPatternAsync(companyId, pattern, page, size);
            return result.Success ? Ok(result) : BadRequest(result);
        }

        /// <summary>
        /// Company'nin tüm cache'ini temizler
        /// </summary>
        [HttpDelete("clear/tenant")]
        public IActionResult ClearTenantCache()
        {
            var companyId = _companyContext.GetCompanyId();
            if (companyId <= 0)
                return BadRequest(new ErrorResult("Geçersiz company ID"));

            var result = _cacheAdminService.ClearTenantCache(companyId);
            return result.Success ? Ok(result) : BadRequest(result);
        }

        /// <summary>
        /// Belirli pattern'deki cache'leri temizler
        /// </summary>
        [HttpDelete("clear/pattern/{pattern}")]
        public IActionResult ClearCacheByPattern(string pattern)
        {
            var companyId = _companyContext.GetCompanyId();
            if (companyId <= 0)
                return BadRequest(new ErrorResult("Geçersiz company ID"));

            var result = _cacheAdminService.ClearCacheByPattern(companyId, pattern);
            return result.Success ? Ok(result) : BadRequest(result);
        }

        /// <summary>
        /// Company cache detaylarını getirir
        /// </summary>
        [HttpGet("tenant/details")]
        public async Task<IActionResult> GetTenantCacheDetails()
        {
            var companyId = _companyContext.GetCompanyId();
            if (companyId <= 0)
                return BadRequest(new ErrorResult("Geçersiz company ID"));

            var result = await _cacheAdminService.GetTenantCacheDetailsAsync(companyId);
            return result.Success ? Ok(result) : BadRequest(result);
        }

        /// <summary>
        /// Cache warmup işlemi başlatır
        /// </summary>
        [HttpPost("warmup")]
        public async Task<IActionResult> WarmupCache([FromBody] CacheWarmupRequestDto request)
        {
            var companyId = _companyContext.GetCompanyId();
            if (companyId <= 0)
                return BadRequest(new ErrorResult("Geçersiz company ID"));

            var result = await _cacheAdminService.WarmupCacheAsync(companyId, request);
            return result.Success ? Ok(result) : BadRequest(result);
        }

        /// <summary>
        /// Real-time cache metrics getirir (Dashboard için)
        /// </summary>
        [HttpGet("metrics/realtime")]
        public async Task<IActionResult> GetRealtimeMetrics()
        {
            var companyId = _companyContext.GetCompanyId();
            if (companyId <= 0)
                return BadRequest(new ErrorResult("Geçersiz company ID"));

            var result = await _cacheAdminService.GetRealtimeMetricsAsync(companyId);
            return result.Success ? Ok(result) : BadRequest(result);
        }

        /// <summary>
        /// Belirli cache key'ini siler
        /// </summary>
        [HttpDelete("key/{key}")]
        public IActionResult DeleteCacheKey(string key)
        {
            var companyId = _companyContext.GetCompanyId();
            if (companyId <= 0)
                return BadRequest(new ErrorResult("Geçersiz company ID"));

            var result = _cacheAdminService.DeleteCacheKey(companyId, key);
            return result.Success ? Ok(result) : BadRequest(result);

        }

        /// <summary>
        /// Cache key'inin değerini getirir (debug amaçlı)
        /// </summary>
        [HttpGet("key/{key}/value")]
        public async Task<IActionResult> GetCacheKeyValue(string key)
        {
            var companyId = _companyContext.GetCompanyId();
            if (companyId <= 0)
                return BadRequest(new ErrorResult("Geçersiz company ID"));

            var result = await _cacheAdminService.GetCacheKeyValueAsync(companyId, key);
            return result.Success ? Ok(result) : BadRequest(result);
        }

        #endregion

        #region Multi-Company Operations (Owner/Admin Only)

        /// <summary>
        /// Tüm şirketlerin cache istatistiklerini getirir (Owner only)
        /// </summary>
        [HttpGet("admin/all-companies/statistics")]
        public async Task<IActionResult> GetAllCompaniesStatistics()
        {
            var result = await _cacheAdminService.GetAllCompaniesStatisticsAsync();
            return result.Success ? Ok(result) : BadRequest(result);
        }

        /// <summary>
        /// Belirli bir şirketin cache detaylarını getirir (Owner only)
        /// </summary>
        [HttpGet("admin/company/{companyId}/details")]
        public async Task<IActionResult> GetSpecificCompanyCacheDetails(int companyId)
        {
            var result = await _cacheAdminService.GetSpecificCompanyCacheDetailsAsync(companyId);
            return result.Success ? Ok(result) : BadRequest(result);
        }

        /// <summary>
        /// Belirli bir şirketin cache'ini temizler (Owner only)
        /// </summary>
        [HttpDelete("admin/company/{companyId}/clear")]
        public IActionResult ClearSpecificCompanyCache(int companyId)
        {
            var result = _cacheAdminService.ClearSpecificCompanyCache(companyId);
            return result.Success ? Ok(result) : BadRequest(result);
        }

        /// <summary>
        /// Çoklu şirket cache temizleme (Owner only)
        /// </summary>
        [HttpPost("admin/bulk-clear")]
        public async Task<IActionResult> BulkClearCompaniesCache([FromBody] BulkCacheOperationRequestDto request)
        {
            var result = await _cacheAdminService.BulkClearCompaniesCacheAsync(request);
            return result.Success ? Ok(result) : BadRequest(result);
        }

        #endregion

        #region Removed - All business logic moved to CacheAdminService

        // Tüm private helper method'lar CacheAdminService ve CacheMetricsService'e taşındı:
        // - GetCompanyCacheStatistics() -> ICacheMetricsService.CalculateStatisticsAsync()
        // - GetRedisServerInfo() -> ICacheMetricsService.GetRedisServerInfoAsync()
        // - GetKeysByPattern() -> ICacheMetricsService.GetKeysByPatternAsync()
        // - GetCompanyCacheDetails() -> ICacheAdminService.GetTenantCacheDetailsAsync()
        // - PerformCacheWarmup() -> ICacheAdminService.WarmupCacheAsync()
        // - GetRealtimeCacheMetrics() -> ICacheAdminService.GetRealtimeMetricsAsync()
        // - GetTopCacheKeys() -> ICacheMetricsService.GetTopCacheKeysAsync()

        #endregion
    }
}
