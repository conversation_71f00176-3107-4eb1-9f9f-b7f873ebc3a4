using System;
using System.Collections.Generic;

namespace Entities.DTOs.Cache
{
    /// <summary>
    /// Cache warmup request DTO
    /// </summary>
    public class CacheWarmupRequestDto
    {
        public bool WarmupMembers { get; set; } = true;
        public bool WarmupPayments { get; set; } = true;
        public bool WarmupMemberships { get; set; } = true;
        public bool WarmupUsers { get; set; } = false;
        public bool WarmupCompanySettings { get; set; } = false;
    }

    /// <summary>
    /// Cache warmup result DTO
    /// </summary>
    public class CacheWarmupResultDto
    {
        public int CompanyId { get; set; }
        public long TotalDuration { get; set; }
        public List<CacheWarmupItemDto> Results { get; set; } = new List<CacheWarmupItemDto>();
        public DateTime CompletedAt { get; set; }
    }

    /// <summary>
    /// Cache warmup item DTO
    /// </summary>
    public class CacheWarmupItemDto
    {
        public string Entity { get; set; } = string.Empty;
        public string Status { get; set; } = string.Empty;
        public string Duration { get; set; } = string.Empty;
    }
}
