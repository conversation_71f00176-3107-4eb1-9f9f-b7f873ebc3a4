using System.Collections.Generic;

namespace Entities.DTOs.Cache
{
    /// <summary>
    /// Sayfalanmış cache key'leri DTO
    /// </summary>
    public class PaginatedCacheKeysDto
    {
        public List<CacheKeyDetailDto> Keys { get; set; } = new List<CacheKeyDetailDto>();
        public PaginationInfoDto Pagination { get; set; } = new PaginationInfoDto();
        public string Pattern { get; set; } = string.Empty;
    }

    /// <summary>
    /// Sayfalama bilgileri DTO
    /// </summary>
    public class PaginationInfoDto
    {
        public int CurrentPage { get; set; }
        public int PageSize { get; set; }
        public int TotalCount { get; set; }
        public int TotalPages { get; set; }
        public bool HasNextPage { get; set; }
        public bool HasPreviousPage { get; set; }
    }
}
