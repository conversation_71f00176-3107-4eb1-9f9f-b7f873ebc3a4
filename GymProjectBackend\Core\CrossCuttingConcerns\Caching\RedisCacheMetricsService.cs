using Core.CrossCuttingConcerns.Logging;
using Entities.DTOs.Cache;
using StackExchange.Redis;
using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Linq;
using System.Threading.Tasks;

namespace Core.CrossCuttingConcerns.Caching
{
    /// <summary>
    /// Redis cache metrics service implementation
    /// Mevcut CacheAdminController'daki tüm metrics logic'ini içerir
    /// </summary>
    public class RedisCacheMetricsService : ICacheMetricsService
    {
        private readonly IConnectionMultiplexer _connectionMultiplexer;
        private readonly ILogService _logger;

        public RedisCacheMetricsService(IConnectionMultiplexer connectionMultiplexer, ILogService logger)
        {
            _connectionMultiplexer = connectionMultiplexer ?? throw new ArgumentNullException(nameof(connectionMultiplexer));
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        }

        public async Task<CacheStatisticsDto> CalculateStatisticsAsync(string pattern)
        {
            try
            {
                var database = _connectionMultiplexer.GetDatabase();
                var server = _connectionMultiplexer.GetServer(_connectionMultiplexer.GetEndPoints().First());

                var keys = server.Keys(pattern: pattern).ToList();
                var totalKeys = keys.Count;
                var totalMemoryUsage = 0L;
                var keysByEntity = new Dictionary<string, int>();

                foreach (var key in keys)
                {
                    try
                    {
                        // Memory usage hesapla (mevcut logic'i koruyoruz)
                        var keyValue = await database.StringGetAsync(key);
                        if (keyValue.HasValue)
                        {
                            // Key name + value + Redis overhead (yaklaşık)
                            var keySize = System.Text.Encoding.UTF8.GetByteCount(key.ToString());
                            var valueSize = System.Text.Encoding.UTF8.GetByteCount(keyValue.ToString());
                            var overhead = 64; // Redis key overhead (yaklaşık)

                            totalMemoryUsage += keySize + valueSize + overhead;
                        }

                        // Entity bazlı gruplandırma (mevcut logic'i koruyoruz)
                        var keyParts = key.ToString().Split(':');
                        if (keyParts.Length >= 3)
                        {
                            var entity = keyParts[2];
                            keysByEntity[entity] = keysByEntity.GetValueOrDefault(entity, 0) + 1;
                        }
                    }
                    catch (Exception ex)
                    {
                        _logger.Error($"Key işlenirken hata: {key}", ex);
                        // Hata durumunda devam et
                    }
                }

                // CompanyId'yi pattern'den çıkar
                var companyId = ExtractCompanyIdFromPattern(pattern);

                return new CacheStatisticsDto
                {
                    CompanyId = companyId,
                    TotalKeys = totalKeys,
                    TotalMemoryUsage = totalMemoryUsage,
                    TotalMemoryUsageMB = Math.Round(totalMemoryUsage / 1024.0 / 1024.0, 2),
                    KeysByEntity = keysByEntity,
                    LastUpdated = DateTime.UtcNow
                };
            }
            catch (Exception ex)
            {
                _logger.Error("Cache istatistikleri hesaplanırken hata", ex);
                throw;
            }
        }

        public async Task<CacheHealthDto> CheckHealthAsync()
        {
            try
            {
                var stopwatch = Stopwatch.StartNew();
                var database = _connectionMultiplexer.GetDatabase();

                // Ping test (mevcut logic'i koruyoruz)
                var pingTime = await database.PingAsync();
                stopwatch.Stop();

                var serverInfo = await GetRedisServerInfoAsync();

                return new CacheHealthDto
                {
                    IsConnected = _connectionMultiplexer.IsConnected,
                    PingTime = pingTime.TotalMilliseconds,
                    ResponseTime = stopwatch.ElapsedMilliseconds,
                    Status = _connectionMultiplexer.IsConnected ? "Healthy" : "Unhealthy",
                    ServerInfo = serverInfo
                };
            }
            catch (Exception ex)
            {
                _logger.Error("Cache health check sırasında hata", ex);
                return new CacheHealthDto
                {
                    IsConnected = false,
                    Status = "Unhealthy",
                    ServerInfo = new { Error = ex.Message }
                };
            }
        }

        public async Task<PaginatedCacheKeysDto> GetKeysByPatternAsync(string pattern, int page, int size)
        {
            try
            {
                var server = _connectionMultiplexer.GetServer(_connectionMultiplexer.GetEndPoints().First());
                var database = _connectionMultiplexer.GetDatabase();

                var allKeys = server.Keys(pattern: pattern).ToList();
                var totalCount = allKeys.Count;
                var totalPages = (int)Math.Ceiling((double)totalCount / size);

                var pagedKeys = allKeys
                    .Skip((page - 1) * size)
                    .Take(size)
                    .ToList();

                var keyDetails = new List<CacheKeyDetailDto>();

                foreach (var key in pagedKeys)
                {
                    try
                    {
                        var ttl = await database.KeyTimeToLiveAsync(key);
                        var type = await database.KeyTypeAsync(key);
                        var keyValue = await database.StringGetAsync(key);

                        keyDetails.Add(new CacheKeyDetailDto
                        {
                            Key = key.ToString(),
                            Type = type.ToString(),
                            TTL = ttl?.TotalSeconds,
                            MemoryUsage = keyValue.HasValue ? keyValue.ToString().Length : 0,
                            CreatedAt = DateTime.UtcNow.AddSeconds(-(ttl?.TotalSeconds ?? 0))
                        });
                    }
                    catch (Exception ex)
                    {
                        _logger.Error($"Key detayı alınırken hata: {key}", ex);
                        keyDetails.Add(new CacheKeyDetailDto
                        {
                            Key = key.ToString(),
                            Type = "Unknown",
                            TTL = null,
                            MemoryUsage = 0,
                            CreatedAt = null
                        });
                    }
                }

                return new PaginatedCacheKeysDto
                {
                    Keys = keyDetails,
                    Pagination = new PaginationInfoDto
                    {
                        CurrentPage = page,
                        PageSize = size,
                        TotalCount = totalCount,
                        TotalPages = totalPages,
                        HasNextPage = page < totalPages,
                        HasPreviousPage = page > 1
                    },
                    Pattern = pattern
                };
            }
            catch (Exception ex)
            {
                _logger.Error("Pattern key'leri alınırken hata", ex);
                throw;
            }
        }

        public async Task<object> GetRedisServerInfoAsync()
        {
            try
            {
                var server = _connectionMultiplexer.GetServer(_connectionMultiplexer.GetEndPoints().First());
                var info = await server.InfoAsync();

                var serverInfo = info.FirstOrDefault(i => i.Key == "Server");
                var memoryInfo = info.FirstOrDefault(i => i.Key == "Memory");

                var version = serverInfo?.FirstOrDefault(s => s.Key == "redis_version");
                var uptime = serverInfo?.FirstOrDefault(s => s.Key == "uptime_in_seconds");
                var usedMem = memoryInfo?.FirstOrDefault(m => m.Key == "used_memory");
                var usedMemHuman = memoryInfo?.FirstOrDefault(m => m.Key == "used_memory_human");
                var maxMem = memoryInfo?.FirstOrDefault(m => m.Key == "maxmemory");
                var maxMemHuman = memoryInfo?.FirstOrDefault(m => m.Key == "maxmemory_human");

                return new
                {
                    Version = version?.Value.ToString() ?? "Unknown",
                    UptimeInSeconds = uptime?.Value.ToString() ?? "0",
                    UsedMemory = usedMem?.Value.ToString() ?? "0",
                    UsedMemoryHuman = usedMemHuman?.Value.ToString() ?? "0B",
                    MaxMemory = maxMem?.Value.ToString() ?? "0",
                    MaxMemoryHuman = maxMemHuman?.Value.ToString() ?? "0B"
                };
            }
            catch (Exception ex)
            {
                _logger.Error("Redis server bilgileri alınırken hata", ex);
                return new { Error = "Server bilgileri alınamadı" };
            }
        }

        public async Task<CacheKeyValueDto> GetKeyValueAsync(string key)
        {
            try
            {
                var database = _connectionMultiplexer.GetDatabase();
                var value = await database.StringGetAsync(key);
                var ttl = await database.KeyTimeToLiveAsync(key);

                return new CacheKeyValueDto
                {
                    Key = key,
                    Value = value.HasValue ? value.ToString() : null,
                    HasValue = value.HasValue,
                    TTL = ttl?.TotalSeconds,
                    Size = value.HasValue ? System.Text.Encoding.UTF8.GetByteCount(value) : 0
                };
            }
            catch (Exception ex)
            {
                _logger.Error($"Cache key değeri alınırken hata: {key}", ex);
                throw;
            }
        }

        public async Task<object[]> GetTopCacheKeysAsync(string pattern, int count)
        {
            try
            {
                var server = _connectionMultiplexer.GetServer(_connectionMultiplexer.GetEndPoints().First());
                var keys = server.Keys(pattern: pattern).Take(count).ToArray();

                var topKeys = new List<object>();
                foreach (var key in keys)
                {
                    var ttl = await _connectionMultiplexer.GetDatabase().KeyTimeToLiveAsync(key);
                    topKeys.Add(new
                    {
                        Key = key.ToString(),
                        TTL = ttl?.TotalSeconds ?? -1,
                        Type = await _connectionMultiplexer.GetDatabase().KeyTypeAsync(key)
                    });
                }

                return topKeys.ToArray();
            }
            catch (Exception ex)
            {
                _logger.Error("Top cache key'leri alınırken hata", ex);
                throw;
            }
        }

        public async Task<CachePerformanceDto> GetPerformanceMetricsAsync(string pattern)
        {
            try
            {
                var database = _connectionMultiplexer.GetDatabase();

                // Response time test (mevcut logic'i koruyoruz)
                var stopwatch = Stopwatch.StartNew();
                await database.PingAsync();
                stopwatch.Stop();

                return new CachePerformanceDto
                {
                    ResponseTime = stopwatch.ElapsedMilliseconds,
                    IsConnected = _connectionMultiplexer.IsConnected,
                    ConnectionCount = _connectionMultiplexer.GetCounters().Interactive.SocketCount
                };
            }
            catch (Exception ex)
            {
                _logger.Error("Cache performans metrikleri alınırken hata", ex);
                throw;
            }
        }

        /// <summary>
        /// Pattern'den CompanyId'yi çıkarır (gym:123:* -> 123)
        /// </summary>
        private int ExtractCompanyIdFromPattern(string pattern)
        {
            try
            {
                var parts = pattern.Split(':');
                if (parts.Length >= 2 && int.TryParse(parts[1], out int companyId))
                {
                    return companyId;
                }
                return -1;
            }
            catch
            {
                return -1;
            }
        }
    }
}
